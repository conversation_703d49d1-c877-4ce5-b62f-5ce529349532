import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  selectActiveTab,
  setActiveTab,
  selectProfile,
  setSidebarOpen,
} from "../../redux/slices/adminDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/AdminSidebar.css";
import { IMAGE_BASE_URL } from "../../utils/constants";

// Logo
import XOSportsLogo from "../../assets/images/XOsports-hub-logo.png";

// Icons
import { MdDashboard, MdPeople, MdVideoLibrary, MdRateReview } from "react-icons/md";
import { FaFileAlt, FaGavel, FaHandshake, FaClipboardList, FaShoppingCart, FaUser, FaCog, FaQuestionCircle } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const AdminSidebar = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const activeTab = useSelector(selectActiveTab);
  const profile = useSelector(selectProfile);

  // Define disabled sections
  const disabledSections = ["reports"];

  // Handle tab click
  const handleTabClick = (tab) => {
    // Prevent navigation for disabled sections
    if (disabledSections.includes(tab)) {
      return;
    }

    dispatch(setActiveTab(tab));

    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/admin/dashboard");
        break;
      case "users":
        navigate("/admin/users");
        break;
      case "content":
        navigate("/admin/content");
        break;
      case "bids":
        navigate("/admin/bids");
        break;
      case "offers":
        navigate("/admin/offers");
        break;
      case "orders":
        navigate("/admin/orders");
        break;
      case "requests":
        navigate("/admin/requests");
        break;
      case "reviews":
        navigate("/admin/reviews");
        break;
      case "cms":
        navigate("/admin/cms");
        break;
      case "faqs":
        navigate("/admin/faqs");
        break;
      case "settings":
        navigate("/admin/settings");
        break;
      default:
        navigate("/admin/dashboard");
    }

    // Close mobile sidebar after navigation
    handleMobileMenuClose();
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  // Handle profile navigation
  const handleProfileClick = () => {
    navigate("/admin/profile");
    // Close mobile sidebar
    if (window.innerWidth <= 768) {
      dispatch(setSidebarOpen(false));
    }
  };



  // Handle mobile sidebar close on menu item click
  const handleMobileMenuClose = () => {
    if (window.innerWidth <= 768) {
      dispatch(setSidebarOpen(false));
    }
  };

  // Coming Soon Badge Component
  const ComingSoonBadge = () => (
    <span className="coming-soon-badge">
      Coming Soon
    </span>
  );

  return (
    <div className="AdminSidebar">
      <div className="AdminSidebar__container">
        {/* Mobile Logo Section */}
        <div className="AdminSidebar__mobile-header">
          <div className="AdminSidebar__logo">
       
            <div className="logo-text">
              <h3>XOSportsHub</h3>
              <span>Admin Panel</span>
            </div>
          </div>
        </div>

        {/* Mobile Profile Section */}
        <div className="AdminSidebar__mobile-profile">
          <div className="profile-card" onClick={handleProfileClick}>
            <div className="profile-avatar">
              {profile.profileImage ? (
                <img src={IMAGE_BASE_URL + profile.profileImage} alt="Admin" />
              ) : (
                <FaUser />
              )}
            </div>
            <div className="profile-details">
              <h4>{profile.firstName} {profile.lastName}</h4>
              <p>{profile.email}</p>
              <span className="profile-role">Administrator</span>
            </div>
          </div>
        </div>

        {/* Navigation Menu */}
        <ul className="AdminSidebar__menu">
          <li
            className={`AdminSidebar__item ${activeTab === "dashboard" ? "active" : ""
              }`}
            onClick={() => handleTabClick("dashboard")}
            data-tooltip="Dashboard Overview"
          >
            <div className="item-content">
              <MdDashboard className="AdminSidebar__icon" />
              <span>Dashboard Overview</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "users" ? "active" : ""
              }`}
            onClick={() => handleTabClick("users")}
            data-tooltip="User Management"
          >
            <div className="item-content">
              <MdPeople className="AdminSidebar__icon" />
              <span>User Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "content" ? "active" : ""
              }`}
            onClick={() => handleTabClick("content")}
            data-tooltip="Content Management"
          >
            <div className="item-content">
              <MdVideoLibrary className="AdminSidebar__icon" />
              <span>Content Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "bids" ? "active" : ""
              }`}
            onClick={() => handleTabClick("bids")}
            data-tooltip="Bid Management"
          >
            <div className="item-content">
              <FaGavel className="AdminSidebar__icon" />
              <span>Bid Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "offers" ? "active" : ""
              }`}
            onClick={() => handleTabClick("offers")}
            data-tooltip="Offer Management"
          >
            <div className="item-content">
              <FaHandshake className="AdminSidebar__icon" />
              <span>Offer Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "orders" ? "active" : ""
              }`}
            onClick={() => handleTabClick("orders")}
            data-tooltip="Order Management"
          >
            <div className="item-content">
              <FaShoppingCart className="AdminSidebar__icon" />
              <span>Order Management</span>
            </div>
          </li>
          <li
            className={`AdminSidebar__item ${activeTab === "reviews" ? "active" : ""
              }`}
            onClick={() => handleTabClick("reviews")}
            data-tooltip="Review Management"
          >
            <div className="item-content">
              <MdRateReview className="AdminSidebar__icon" />
              <span>Review Management</span>
            </div>
          </li>
          <li
            className={`AdminSidebar__item ${activeTab === "cms" ? "active" : ""
              }`}
            onClick={() => handleTabClick("cms")}
            data-tooltip="CMS Pages"
          >
            <div className="item-content">
              <FaFileAlt className="AdminSidebar__icon" />
              <span>CMS Pages</span>
            </div>
          </li>
          <li
            className={`AdminSidebar__item ${activeTab === "faqs" ? "active" : ""
              }`}
            onClick={() => handleTabClick("faqs")}
            data-tooltip="FAQ Management"
          >
            <div className="item-content">
              <FaQuestionCircle className="AdminSidebar__icon" />
              <span>FAQ Management</span>
            </div>
          </li>


          <li
            className={`AdminSidebar__item ${activeTab === "settings" ? "active" : ""
              }`}
            onClick={() => handleTabClick("settings")}
            data-tooltip="Settings"
          >
            <div className="item-content">
              <FaCog className="AdminSidebar__icon" />
              <span>Settings</span>
            </div>
          </li>
          <li
            className={`AdminSidebar__item ${activeTab === "requests" ? "active" : ""} ${disabledSections.includes("requests") ? "disabled" : ""}`}
            onClick={() => handleTabClick("requests")}
            data-tooltip="Request Management"
          >
            <div className="item-content">
              <FaClipboardList className="AdminSidebar__icon" />
              <span>Request Management</span>
            </div>
            {disabledSections.includes("requests")}
          </li>

        </ul>

        {/* Mobile Logout Section */}
        <div className="AdminSidebar__mobile-actions">
          <div
            className="AdminSidebar__item logout-item"
            onClick={handleLogout}
            data-tooltip="Logout"
          >
            <div className="item-content">
              <IoLogOut className="AdminSidebar__icon" />
              <span>Logout</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
