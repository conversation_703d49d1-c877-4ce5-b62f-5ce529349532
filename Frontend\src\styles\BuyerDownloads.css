.BuyerDownloads {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following consistent dashboard pattern */
.BuyerDownloads .table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
}

.BuyerDownloads .table th,
.BuyerDownloads .table td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.BuyerDownloads .table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.BuyerDownloads .table tbody tr {
  transition: background-color 0.2s ease;
}

.BuyerDownloads .table tbody tr:hover {
  background-color: var(--primary-light-color);
}

.BuyerDownloads .table tr:last-child td {
  border-bottom: none;
}

/* Content item styling - Following consistent pattern */
.content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerDownloads .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerDownloads .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerDownloads .content-info {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.BuyerDownloads .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerDownloads .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerDownloads .content-type {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-top: 2px;
}

.BuyerDownloads .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerDownloads .status-badge.downloaded {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerDownloads .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

/* Action buttons styling - Following consistent dashboard pattern */
.BuyerDownloads .action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.BuyerDownloads .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--basefont);

}

.BuyerDownloads .download-btn {
  background-color: var(--primary-color);
  color: var(--white);
}

.BuyerDownloads .download-btn:hover:not(:disabled) {
  background-color: var(--secondary-color);
  transform: scale(1.05);
}

.BuyerDownloads .download-btn:disabled {
  background-color: var(--dark-gray);
  cursor: not-allowed;
  opacity: 0.6;
}

.download-view-btn {
     background-color: transparent;
    font-size: var(--heading6);
    color: var(--text-color);
  border: none;
}

 .download-view-btn:hover {
 

  transform: scale(1.05);
}

/* Spinner for loading state - Following consistent loading pattern */
.BuyerDownloads .spinner {
  width: 14px;
  height: 14px;
  border: 2px solid var(--white);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.BuyerDownloads .table-cells {
  font-size: var(--smallfont);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Tab Navigation - Following consistent tab pattern */
.content-tabs {
  display: flex;
  gap: var(--basefont);
  margin-bottom: var(--heading5);
  border-bottom: 2px solid var(--light-gray);
  background-color: var(--white);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  box-shadow: var(--box-shadow-light);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--basefont) var(--heading5);
  background: none;
  border: none;
  color: var(--dark-gray);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.tab-btn:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-1px);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: var(--primary-light-color);
  font-weight: 600;
}

/* Custom Content Grid - Following consistent card pattern */
.custom-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--heading5);
  margin-top: var(--basefont);
}

.custom-content-card {
  background: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--light-gray);
}

.custom-content-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.content-thumbnail {
  height: 180px;
  overflow: hidden;
  background: var(--bg-gray);
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.custom-content-card:hover .content-thumbnail img {
  transform: scale(1.05);
}



.content-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
  line-height: 1.4;
}

.request-title {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin-bottom: var(--basefont);

}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;

  font-size: var(--smallfont);
}



.completion-date {
  color: var(--dark-gray);
}

.download-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--extrasmallfont);
  width: 100%;
  padding: var(--smallfont);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--basefont);
}

.download-btn:hover {
  background: var(--secondary-color);
  transform: scale(1.02);
}

/* Custom Content Table Styling - Following consistent table pattern */
.content-type-badge {
  background: var(--primary-light-color);
  color: var(--primary-color);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
}

.access-btn {
  background: var(--white);
  color: var(--secondary-color);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.access-btn:hover {
color: var(--primary-color);
  transform: scale(1.05);
}

/* Request title styling in table */
.BuyerDownloads__table .request-title {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 500;
  margin-top: 2px;
}

/* Empty state - Following consistent empty state pattern */
.BuyerDownloads__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
  background: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.BuyerDownloads__empty h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 var(--smallfont) 0;
  font-weight: 600;
}

.BuyerDownloads__empty p {
  font-size: var(--basefont);
  color: var(--dark-gray);
}

/* Pagination - Following consistent pagination pattern */
.BuyerDownloads__pagination {
  margin-top: var(--heading5);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--extrasmallfont);
}

.BuyerDownloads .no-data-message {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
  font-size: var(--heading6);
  background: var(--primary-light-color);
  border-radius: var(--border-radius);
  margin: var(--heading5) 0;
}

/* Responsive styles - Following consistent responsive pattern */
@media (max-width: 1024px) {
  .custom-content-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--basefont);
  }

  .BuyerDownloads .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerDownloads .table {
    font-size: var(--extrasmallfont);
  }

  .BuyerDownloads .table th,
  .BuyerDownloads .table td {
    padding: 8px 6px;
  }

  .tab-btn {
    padding: var(--smallfont) var(--basefont);
    font-size: var(--smallfont);
  }

  .custom-content-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--smallfont);
  }


}

@media (max-width: 480px) {
  .BuyerDownloads .table {
    overflow-x: auto;
  }

  .content-tabs {
    flex-direction: column;
    gap: 0;
    box-shadow: none;
    border-bottom: none;
  }

  .tab-btn {
    border-radius: 0;
    border-bottom: 1px solid var(--light-gray);
  }

  .custom-content-grid {
    grid-template-columns: 1fr;
  }

  .BuyerDownloads__empty {
    padding: var(--heading5);
  }
}
